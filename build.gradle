group 'com.ryanheise.audio_session'
version '1.0'
def args = ["-Xlint:deprecation","-Xlint:unchecked"]

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

project.getTasks().withType(JavaCompile) {
    options.compilerArgs.addAll(args)
}

apply plugin: 'com.android.library'

android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
        namespace 'com.ryanheise.audio_session'
    }
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 16
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }
}

dependencies {
    implementation "androidx.media2:media2-session:1.2.1"
}
