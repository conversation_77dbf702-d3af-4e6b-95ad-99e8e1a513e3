buildscript {
    ext.kotlin_version = "1.7.22"
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    dependencies {
        classpath "com.android.tools.build:gradle:7.2.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jlleitschuh.gradle:ktlint-gradle:11.0.0"
    }
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"
apply plugin: "org.jlleitschuh.gradle.ktlint"

group "vn.hunghd.flutterdownloader"
version "1.0-SNAPSHOT"

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

android {
    namespace "vn.hunghd.flutterdownloader"
    compileSdk 32

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
    }

    defaultConfig {
        minSdk 19
        targetSdk 32
    }
}

dependencies {
    implementation "androidx.work:work-runtime:2.7.1"
    implementation "androidx.annotation:annotation:1.5.0"
    implementation "androidx.core:core-ktx:1.9.0"
}
