group 'io.flutter.plugins.camera'
version '1.0-SNAPSHOT'
def args = ["-Xlint:deprecation","-Xlint:unchecked","-Werror"]

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

project.getTasks().withType(JavaCompile){
    options.compilerArgs.addAll(args)
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
        disable 'GradleDependency'
        baseline file("lint-baseline.xml")
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }


    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}

dependencies {
    compileOnly 'androidx.annotation:annotation:1.1.0'
    testImplementation 'junit:junit:4.12'
    testImplementation 'org.mockito:mockito-inline:3.11.1'
    testImplementation 'androidx.test:core:1.3.0'
    testImplementation 'org.robolectric:robolectric:4.3'
}
