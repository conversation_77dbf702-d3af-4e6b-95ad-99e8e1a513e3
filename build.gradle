group "com.lyokone.location"
version "1.0-SNAPSHOT"

buildscript {
    ext.kotlin_version = "1.9.0"
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    dependencies {
        classpath "com.android.tools.build:gradle:7.4.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath("org.jlleitschuh.gradle:ktlint-gradle:11.5.0")
    }
}

repositories {
    google()
    mavenCentral()
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"
apply plugin: "org.jlleitschuh.gradle.ktlint"

android {
    namespace "com.lyokone.location"
    compileSdk 33

    defaultConfig {
        minSdk 16
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
        allWarningsAsErrors = true
    }
}

dependencies {
    compileOnly "androidx.annotation:annotation:1.6.0"

    api "com.google.android.gms:play-services-location:21.0.1"
    implementation "androidx.core:core-ktx:1.10.1"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
}
