group 'ru.surfstudio.otp_autofill'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.7.21'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 33

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 33
    }
    lintOptions {
        disable 'InvalidPackage'
    }

    namespace 'ru.surfstudio.otp_autofill'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.google.android.gms:play-services-auth:20.3.0'
    implementation 'com.google.android.gms:play-services-auth-api-phone:17.4.0'
    implementation "androidx.activity:activity:1.5.1"
    implementation "androidx.fragment:fragment:1.5.4"
}
