<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    android:backgroundTint="@color/black_text"
    android:clickable="true"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:filterTouchesWhenObscured="true"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/masterView"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:orientation="vertical"
        android:background="@color/exo_styled_error_message_background"
        android:visibility="visible"></LinearLayout>
</LinearLayout>