package com.viettel.citizens.hanoi

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.multidex.MultiDex
import com.gg.gapo.core.remoteconfig.GapoRemoteConfig
import com.gg.gapo.core.ui.notification.inapp.GapoInAppNotification
import com.gg.gapo.core.user.manager.UserManager
import com.gg.gapo.core.utilities.lifecycle.GapoDefaultLifecycleObserver
import com.gg.gapo.core.utilities.livedata.appForegroundLiveData
import com.gg.gapo.core.utilities.resources.GapoGlobalResources
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.livekit.call.center.CallCenterV2
import com.gg.gapo.feature.auth.center.AuthCenter
import com.gg.gapo.messenger.MessengerCenter
import com.google.android.gms.tasks.Task
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.perf.FirebasePerformance
import com.google.firebase.remoteconfig.ConfigUpdate
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.viettel.citizens.hanoi.ui.Logger
import io.flutter.app.FlutterApplication
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.PluginRegistry
import io.flutter.plugin.common.PluginRegistry.PluginRegistrantCallback
import io.reactivex.plugins.RxJavaPlugins
import org.koin.android.ext.android.get
import timber.log.Timber

class Application : FlutterApplication(), PluginRegistrantCallback {
    private val MOCHA_CHANNEL = "mocha.sdk.viettel.com/call-platform"

    private val mochaChannel: MethodChannel? = null

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(GapoGlobalResources.attachBaseContext(base))
        MultiDex.install(this)
    }

    override fun onCreate() {
        super.onCreate()
        //        FlutterFirebaseMessagingService.setPluginRegistrant(this);
        initFirebase(this)
        Logger.init()
        setupTimber()
        RxJavaPlugins.setErrorHandler { Timber.e(it.message) }
        addLifecycleObserver()
        initCenter()
    }

    private fun initCenter() {
        get<GapoInAppNotification>().init()
        get<UserManager>().init()
        get<WorkspaceManager>().init()
        get<CallCenterV2>().init()
        get<MessengerCenter>().init()
        get<AuthCenter>().init()
        get<GapoRemoteConfig>().fetchAndActivate()
    }

    private fun addLifecycleObserver() {
        ProcessLifecycleOwner.get()
                .lifecycle
                .addObserver(
                        object : GapoDefaultLifecycleObserver() {
                            override fun onStart(owner: LifecycleOwner) {
                                appForegroundLiveData.value = true
                            }

                            override fun onStop(owner: LifecycleOwner) {
                                appForegroundLiveData.value = false
                            }
                        }
                )
    }

    private fun setupTimber() {
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
    }

    override fun registerWith(registry: PluginRegistry) {
        FirebaseCloudMessagingPlugin.registerWith(registry)
        //
        // FirebaseMessagingPlugin.registerWith(registry.registrarFor("io.flutter.plugins.firebasemessaging.FirebaseMessagingPlugin"));
        //        FlutterAndroidLifecyclePlugin.registerWith(
        //                registry.registrarFor(
        //
        // "io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin"));
        //
        // LocalAuthPlugin.registerWith(registry.registrarFor("io.flutter.plugins.localauth.LocalAuthPlugin"));
    }

    private fun initFirebase(application: Application) {
        FirebaseApp.initializeApp(application)
        FirebaseAnalytics.getInstance(application).setAnalyticsCollectionEnabled(true)
        FirebasePerformance.getInstance().isPerformanceCollectionEnabled = true
        val builder = FirebaseRemoteConfigSettings.Builder()
        builder.setFetchTimeoutInSeconds(60L)
        builder.setMinimumFetchIntervalInSeconds(600L)
        FirebaseRemoteConfig.getInstance().setConfigSettingsAsync(builder.build())
        FirebaseRemoteConfig.getInstance().fetchAndActivate().addOnCompleteListener {
                task: Task<Boolean> ->
            if (task.isSuccessful) {
                val updated = task.result
                Timber.d(
                        "FirebaseRemoteConfig",
                        "Fetch and activate succeeded. Config params updated: $updated"
                )
            } else {
                Timber.d("FirebaseRemoteConfig", "Fetch failed.")
            }
            val mapConfig = FirebaseRemoteConfig.getInstance().all
            if (BuildConfig.DEBUG) {
                val keys: Set<String> = mapConfig.keys
                for (key in keys) {
                    val value = mapConfig[key]
                    Timber.e(
                            "FirebaseRemoteConfig",
                            """
                                (key, value): $key
                                ${value!!.asString()}
                                """.trimIndent()
                    )
                }
            }
        }
        FirebaseRemoteConfig.getInstance()
                .addOnConfigUpdateListener(
                        object : ConfigUpdateListener {
                            override fun onUpdate(configUpdate: ConfigUpdate) {
                                Timber.e(
                                        "FirebaseRemoteConfig",
                                        "addOnConfigUpdateListener onUpdate: " +
                                                configUpdate.updatedKeys
                                )
                                FirebaseRemoteConfig.getInstance().fetchAndActivate()
                            }

                            override fun onError(error: FirebaseRemoteConfigException) {
                                Timber.e(
                                        "FirebaseRemoteConfig",
                                        "addOnConfigUpdateListener onError",
                                        error
                                )
                            }
                        }
                )
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task: Task<String?> ->
            if (task.isSuccessful) {
                val token = task.result
                // MochaSDK.setRegId(token);
            }
        }
    }
}
