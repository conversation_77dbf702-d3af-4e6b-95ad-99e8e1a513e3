package com.viettel.citizens.hanoi.ui.decoder.avc;

import android.util.Pair;

import com.viettel.citizens.hanoi.ui.decoder.data.DataStruct;
import com.viettel.citizens.hanoi.ui.decoder.player.VtsPlayer;

import java.nio.ByteBuffer;
import java.util.Arrays;

public class AvcSplitter extends Thread {
    private static final String TAG = AvcSplitter.class.getName();
    private static final int HEADER_LENGTH = 12;
    private static final byte[] NALUStartCode = {0, 0, 0, 1};
    private final VtsPlayer player;
    private boolean isRunning;

    private final boolean gotParams = false;
    private final boolean gotPPS = false;
    private final boolean gotSPS = false;

    private byte[] spSet;

    private int retry = 0;

    public AvcSplitter(VtsPlayer player) {
        this.player = player;
    }

    public void startSplitter() {
        start();
        this.isRunning = true;
    }

    public void stopSplitter() {
        this.isRunning = false;
    }

    @Override
    public void run() {
        try {
            if (!isRunning) {
                return;
            }

            int maxRetry;
            while (!this.player.isStop()) {
                if (!isRunning) {
                    break;
                }
                Pair<Long, DataStruct> ret = this.player.getRtpQueue().dequeue();
                if (!this.gotParams) {
                    maxRetry = 1;
                } else {
                    maxRetry = 100;
                }

                if (ret.second != null) {
                    this.retry = 0;
                    splitRtpPackage(ret.second);
                } else if (this.retry < maxRetry) {
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    this.retry++;
                } else {
                    this.retry = 0;
                    this.player.getRtpQueue().next();
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void splitRtpPackage(DataStruct dataStruct) {
        if (dataStruct == null) {
            return;
        }
        byte[] totalBytes = dataStruct.getVideoData();
        int payloadSize = totalBytes.length;
        int check = totalBytes[1];
//        Log.i("BYTE","------> "+check);
        if(check!=1){stopSplitter();return;}
        if (payloadSize > 8) {
            byte[] h264Raw = Arrays.copyOfRange(totalBytes, 12, totalBytes.length);
            long timestamp = bytesToInt(Arrays.copyOfRange(totalBytes, 2, 10));
            int nalType = h264Raw[0] & 31;
//            Log.i("AVC H.264","nalType ----> "+nalType);

            DataStruct h264Package = null;
            h264Package = new DataStruct(concatenateByteArrays(NALUStartCode, h264Raw), timestamp, nalType == 7);
            onH264PackageReceived(h264Package);
        }
    }

    private void onH264PackageReceived(DataStruct dataStruct) {
        this.player.getDataQueue().enqueue(dataStruct);
    }

    private static byte[] concatenateByteArrays(byte[] a, byte[] b) {
        byte[] result = new byte[(a.length + b.length)];
        System.arraycopy(a, 0, result, 0, a.length);
        System.arraycopy(b, 0, result, a.length, b.length);
        return result;
    }


    private long bytesToInt(byte[] range) {
        return ByteBuffer.wrap(range).getInt();
    }
}
