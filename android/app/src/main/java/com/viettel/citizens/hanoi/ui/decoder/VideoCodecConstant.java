package com.viettel.citizens.hanoi.ui.decoder;

public interface VideoCodecConstant {
    // video codec
    String VIDEO_CODEC = "video/avc";

    // frame per seconds
    int VIDEO_FPS = 30;

    // i frame interval
    int VIDEO_FI = 2;

    // video bitrate
    int VIDEO_BITRATE = 3000 * 1000;

    //Video resolution
    int VIDEO_WIDTH = 1920;
    int VIDEO_HEIGHT = 1080;

    int HEADER_LENGTH = 12;
}

