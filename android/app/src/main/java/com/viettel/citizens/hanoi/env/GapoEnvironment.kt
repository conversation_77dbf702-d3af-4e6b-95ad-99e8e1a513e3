package com.viettel.citizens.hanoi.env

object GapoEnvironment {

    const val ANDROID_CLIENT_ID = "qixdyecuaxji05u9kago"

 //   const val WEB_BASE_URL = "https://gapowork.vn/"

 //   const val API_BASE_URL = "https://ihanoi-staging-api.gapowork.vn/"

   // const val MOBILE_PUSH_API_BASE_URL = "https://ihanoi-staging-api.gapowork.vn/"

  //  const val UPLOAD_API_BASE_URL = "https://ihanoi-staging-upload.gapowork.vn/"

  //  const val MESSENGER_API_BASE_URL = "https://ihanoi-staging-messenger.gapowork.vn/"

  //  const val MQTT_TCP_URL = "ssl://ihanoi-staging-mqtts.gapowork.vn:31883"

 //   const val LIVEKIT_WSS = "wss://ihanoi-livekit-staging.gapowork.vn"


    const val WEB_BASE_URL = "https://gapowork.vn/"

    const val API_BASE_URL = "https://ihanoi-api.gapo.vn/"

    const val MOBILE_PUSH_API_BASE_URL = "https://ihanoi-api.gapo.vn/"

    const val UPLOAD_API_BASE_URL = "https://ihanoi-upload.gapo.vn/"

    const val MESSENGER_API_BASE_URL = "https://ihanoi-messenger.gapo.vn/"

    const val MQTT_TCP_URL = "ssl://ihanoi-mqtts.gapo.vn:31883"

    const val LIVEKIT_WSS = "wss://ihanoi-livekit.gapo.vn"

}
