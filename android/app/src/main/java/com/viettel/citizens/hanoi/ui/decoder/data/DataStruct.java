package com.viettel.citizens.hanoi.ui.decoder.data;

import java.util.Comparator;

public class DataStruct implements Comparator {
    public static final int HEVC_VPS_NUT = 32;
    public static final int HEVC_SPS_NUT = 33;
    public static final int HEVC_PPS_NUT = 34;

    private long mTimeNum;
    private final byte[] mVideoData;
    private boolean isParams;

    public DataStruct(byte[] data) {
        this.mVideoData = data;
    }

    public DataStruct(byte[] data, long mTimeNum, boolean isParams) {
        this.mVideoData = data;
        this.mTimeNum = mTimeNum;
        this.isParams = isParams;
    }

    public boolean isParams() {
        return isParams;
    }

    public void setParams(boolean idr) {
        this.isParams = idr;
    }

    public byte[] getVideoData() {
        return mVideoData;
    }

    @Override
    public int compare(Object o1, Object o2) {
        return 0;
    }
}
