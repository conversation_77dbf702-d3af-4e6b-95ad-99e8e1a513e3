package com.viettel.citizens.hanoi.initializer

import android.content.Context
import androidx.startup.Initializer
import com.bumptech.glide.Glide
import com.gg.gapo.core.utilities.glide.GapoGlide

/**
 * <AUTHOR>
 * @since 01/01/2021
 */
class GlideInitializer : Initializer<Glide> {
    override fun create(context: Context): Glide {
        return GapoGlide.get(context)
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}
