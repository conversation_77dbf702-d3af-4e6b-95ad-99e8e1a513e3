package com.viettel.citizens.hanoi.notification.fcm.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.viettel.citizens.hanoi.notification.fcm.GapoFcmService
import com.viettel.citizens.hanoi.notification.fcm.cache.deleteFcmInboxMessage

internal class FcmDeleteIntentReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        val groupKey = intent.getStringExtra(GapoFcmService.GROUP_KEY)
        if (!groupKey.isNullOrEmpty()) {
            context.deleteFcmInboxMessage(groupKey)
        }
    }

    companion object {

        fun newIntent(context: Context, groupKey: String) =
            Intent(context, FcmDeleteIntentReceiver::class.java).apply {
                putExtra(GapoFcmService.GROUP_KEY, groupKey)
            }
    }
}
