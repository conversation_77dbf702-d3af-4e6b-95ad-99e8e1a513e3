package com.viettel.citizens.hanoi.home.domain.chat.usecase

import com.gg.gapo.core.utilities.result.Result
import com.viettel.citizens.hanoi.home.domain.chat.HomeChatRepository

internal class FetchUnreadConversationCountUseCase(
    private val chatRepository: HomeChatRepository
) {

    suspend operator fun invoke(): Result<Int> {
        return try {
            val count = chatRepository.fetchUnreadConversationCount()
            Result.Success(count)
        } catch (e: Exception) {
            Result.Error(e)
        }
    }
}
