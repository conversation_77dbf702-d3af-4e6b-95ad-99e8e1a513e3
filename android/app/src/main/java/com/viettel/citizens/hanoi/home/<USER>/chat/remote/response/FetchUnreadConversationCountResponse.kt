package com.viettel.citizens.hanoi.home.data.chat.remote.response

import com.google.gson.annotations.SerializedName

internal data class FetchUnreadConversationCountResponse(
    @SerializedName("data")
    val unread: List<UnreadFolder>?
) {
    data class UnreadFolder(
        @SerializedName("alias")
        val alias: String?,
        @SerializedName("unread_count")
        val unreadCount: Int?
    ) {

        val isUnreadFolder: <PERSON><PERSON><PERSON>
            get() = alias == "unread"
    }
}
