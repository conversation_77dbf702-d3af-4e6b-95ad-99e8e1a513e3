package com.viettel.citizens.hanoi.deeplink.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.Uri
import com.airbnb.deeplinkdispatch.DeepLinkHandler
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import timber.log.Timber

/**
 * <AUTHOR>
 * @since 15/12/2021
 */
internal class WorkspaceDeepLinkReceiver : BroadcastReceiver(), KoinComponent {

    private val workspaceManager by inject<WorkspaceManager>()

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.getBooleanExtra(DeepLinkHandler.EXTRA_SUCCESSFUL, false)) {
            val deepLink = intent.getStringExtra(DeepLinkHandler.EXTRA_URI)
            if (!deepLink.isNullOrEmpty()) {
                val deepLinkUri = try {
                    Uri.parse(deepLink)
                } catch (e: Exception) {
                    null
                } ?: return
                val workspaceId = deepLinkUri.getQueryParameter("workspace_id") ?: return
                Timber.e("deepLinkUri workspaceId = $workspaceId")
                val currentWorkspaceId = workspaceManager.currentWorkspaceId
                if (workspaceId != currentWorkspaceId) {
                    workspaceManager.switchWorkspace(workspaceId)
                }
            }
        } else {
            val errorMessage = intent.getStringExtra(DeepLinkHandler.EXTRA_ERROR_MESSAGE)
        }
    }
}
