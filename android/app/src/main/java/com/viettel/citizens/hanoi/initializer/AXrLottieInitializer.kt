package com.viettel.citizens.hanoi.initializer

import android.content.Context
import androidx.startup.Initializer
import com.aghajari.rlottie.AXrLottie
import com.aghajari.rlottie.extension.GZipFileExtension
import com.aghajari.rlottie.network.AXrLottieFetchResult
import com.aghajari.rlottie.network.AXrLottieNetworkFetcher
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import timber.log.Timber
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @since 14/01/2021
 */
class AXrLottieInitializer : Initializer<Unit> {

    override fun create(context: Context) {
        AXrLottie.init(context)
        AXrLottie.setMaxNetworkCacheSize(MAX_NETWORK_CACHE_SIZE)
        AXrLottie.setNetworkFetcher(GapoAXrLottieNetworkFetcher())
        AXrLottie.addFileExtension(GZipFileExtension(".gps"))
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }

    companion object {
        private const val MAX_NETWORK_CACHE_SIZE = 100
    }
}

private class GapoAXrLottieNetworkFetcher : AXrLottieNetworkFetcher() {

    private val okHttpClient = OkHttpClient.Builder()
        .followRedirects(true)
        .followSslRedirects(true)
        .callTimeout(connectTimeout.toLong(), TimeUnit.MILLISECONDS)
        .readTimeout(readTimeout.toLong(), TimeUnit.MILLISECONDS)
        .retryOnConnectionFailure(true)
        .build()

    override fun fetchSync(url: String): AXrLottieFetchResult {
        val request = Request.Builder().url(url).build()
        val response = okHttpClient.newCall(request).execute()
        return GapoAXrLottieFetchResult(response)
    }
}

private class GapoAXrLottieFetchResult(
    private val response: Response
) : AXrLottieFetchResult {

    override fun close() {
        try {
            response.body?.close()
            response.close()
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    override fun isSuccessful(): Boolean = response.isSuccessful

    override fun bodyByteStream(): InputStream = response.body?.byteStream() ?: throw IOException()

    override fun contentType(): String? = response.body?.contentType()?.type

    override fun error(): String? = response.body?.string()
}
