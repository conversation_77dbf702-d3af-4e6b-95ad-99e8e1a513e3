package com.viettel.citizens.hanoi.notification.fcm.cache

import android.content.Context
import android.content.SharedPreferences
import androidx.core.app.NotificationCompat
import com.gg.gapo.core.utilities.preferences.get
import com.gg.gapo.core.utilities.preferences.getSharedPreferences
import com.gg.gapo.core.utilities.preferences.set
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import timber.log.Timber

private const val FCM_INBOX_MESSAGE_SHARED_PREF_NAME = "FCM_INBOX_MESSAGE_SHARED_PREF_NAME"

private val Context.getFcmInboxMessageSharedPreferences: SharedPreferences
    get() = getSharedPreferences(FCM_INBOX_MESSAGE_SHARED_PREF_NAME)

internal fun Context.getFcmInboxMessage(key: String): List<FcmInboxMessage> {
    val sharedPreferences = getFcmInboxMessageSharedPreferences
    val json = sharedPreferences.get<String>(key)
    if (json.isEmpty()) return emptyList()
    return try {
        Gson().fromJson(json, object : TypeToken<List<FcmInboxMessage>>() {}.type)
    } catch (e: Exception) {
        Timber.e(e)
        emptyList()
    }
}

internal fun Context.saveFcmInboxMessage(key: String, messages: List<FcmInboxMessage>) {
    val sharedPreferences = getFcmInboxMessageSharedPreferences
    try {
        sharedPreferences[key] = Gson().toJson(messages)
    } catch (e: Exception) {
        Timber.e(e)
    }
}

internal fun Context.deleteFcmInboxMessage(key: String): Boolean {
    val sharedPreferences = getFcmInboxMessageSharedPreferences
    return sharedPreferences.edit().remove(key).commit()
}

internal data class FcmInboxMessage(
    @SerializedName("id") val id: String,
    @SerializedName("message") val message: String
)

internal fun List<FcmInboxMessage>.toInboxStyle(): NotificationCompat.InboxStyle {
    val style = NotificationCompat.InboxStyle()
    forEach { style.addLine(it.message) }
    return style
}
