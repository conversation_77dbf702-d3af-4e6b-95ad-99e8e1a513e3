package com.viettel.citizens.hanoi.initializer

import android.content.Context
import androidx.core.provider.FontRequest
import androidx.emoji.bundled.BundledEmojiCompatConfig
import androidx.emoji.text.EmojiCompat
import androidx.emoji.text.FontRequestEmojiCompatConfig
import androidx.startup.Initializer
import com.gg.gapo.core.ui.R
import com.vanniktech.emoji.EmojiManager
import com.vanniktech.emoji.googlecompat.GoogleCompatEmojiProvider

/**
 * <AUTHOR>
 * @since 01/01/2021
 */
class EmojiInitializer : Initializer<Unit> {
    override fun create(context: Context) {
        EmojiCompat.init(BundledEmojiCompatConfig(context))
        EmojiManager.install(
            GoogleCompatEmojiProvider(
                EmojiCompat.init(
                    FontRequestEmojiCompatConfig(
                        context,
                        FontRequest(
                            "com.google.android.gms.fonts",
                            "com.google.android.gms",
                            "Noto Color Emoji Compat",
                            R.array.com_google_android_gms_fonts_certs
                        )
                    ).setReplaceAll(true)
                )
            )
        )
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}
