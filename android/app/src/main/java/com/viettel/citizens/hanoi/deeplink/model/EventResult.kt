package com.viettel.citizens.hanoi.deeplink.model

/**
 * <AUTHOR>
 * @since 30/7/24
 */
internal sealed class EventResult<out T : Any> {

    data class Success<out T : Any>(val data: T) : EventResult<T>()
    data class Error(val msg: String) : EventResult<Nothing>()
    object Loading : EventResult<Nothing>()

    override fun toString(): String {
        return when (this) {
            is Success<*> -> "Success[data=$data]"
            is Error -> "Error[exception=$msg]"
            is Loading -> "Loading"
        }
    }
}
