package com.viettel.citizens.hanoi.ui.decoder.player;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

//import com.viettel.citizens.hanoi.ui.camera.PhcVideoView;
import com.viettel.citizens.hanoi.ui.camera.PhcVideoView;
import com.viettel.citizens.hanoi.ui.decoder.avc.AvcDecoder;
import com.viettel.citizens.hanoi.ui.decoder.avc.AvcSplitter;
import com.viettel.citizens.hanoi.ui.decoder.data.DataQueue;
import com.viettel.citizens.hanoi.ui.decoder.data.RtpQueue;
import com.viettel.citizens.hanoi.ui.decoder.hevc.HevcDecoder;
import com.viettel.citizens.hanoi.ui.decoder.hevc.HevcSplitter;
import com.viettel.citizens.hanoi.ui.decoder.socket.VideoSocket;
import io.flutter.plugin.common.MethodChannel;

public class VtsPlayer {
    public static final String AVC_PLAYER = "AVC";
    public static final String HEVC_PLAYER = "HEVC";

    private VideoSocket videoSocket;

    //h265
//    private HevcSocket hevcSocket;
    private HevcSplitter hevcSplitter;
    private HevcDecoder hevcDecoder;

    //h264
//    private AvcSocket avcSocket;
    private AvcSplitter avcSplitter;
    private AvcDecoder avcDecoder;

    //AAC
    // private AacDecoder aacDecoder;
    // private AudioSocket audioSocket;
    // private AudioSplitter audioSplitter;

    private RtpQueue rtpQueue;
    private DataQueue dataQueue;
    // private DataQueueAudio dataQueueAudio;
    private PhcVideoView videoView;
    private String urlSocket = "";
    // private String urlSocketAudio = "";
    private boolean isStop;
    private IPlayerState iPlayerState;
    private boolean isPlaying;
    private boolean isRecording = false;
    private MethodChannel methodChannel;

    public RtpQueue getRtpQueue() {
        return this.rtpQueue;
    }

    public DataQueue getDataQueue() {
        return dataQueue;
    }

    // public DataQueueAudio getDataQueueAudio() {
    //     return dataQueueAudio;
    // }

    public PhcVideoView getVideoView() {
        return videoView;
    }

    public boolean isStop() {
        return this.isStop;
    }

    public void initAvcPlayer() {
//        this.avcSocket = new AvcSocket(this);
        this.avcSplitter = new AvcSplitter(this);
        this.avcDecoder = new AvcDecoder(this);
    }

    public void initHevcPlayer() {
//        this.hevcSocket = new HevcSocket(this, this.methodChannel);
        this.hevcSplitter = new HevcSplitter(this);
        this.hevcDecoder = new HevcDecoder(this);
    }

    // public void initAacPlayer() {
    //     this.audioSocket = new AudioSocket(this);
    //     this.audioSplitter = new AudioSplitter(this);
    //     this.aacDecoder = new AacDecoder(this);
    // }

    public VtsPlayer(MethodChannel methodChannel) {
        this.rtpQueue = new RtpQueue();
        this.dataQueue = new DataQueue();
        // this.dataQueueAudio = new DataQueueAudio();
        this.methodChannel = methodChannel;
        this.videoSocket = new VideoSocket(this);
        initHevcPlayer();
        initAvcPlayer();
    }

    public void setUpPlayer(PhcVideoView videoView, String urlSocket, String token) {
        this.videoView = videoView;
        this.urlSocket = urlSocket;
        startPlayer(token);
    }

    // public void setUpPlayerAudio(String urlSocket) {
    //     this.urlSocketAudio = urlSocket.replace("evup","audio");
    //     initAacPlayer();
    //     startAudioPlayer();
    // }

    // public boolean IsRecording(){return this.isRecording;}

    // public void setRecording(boolean isRecording){
    //     this.isRecording = isRecording;
    // }

    public void sleep20milSec() {
        try {
            Thread.sleep(10);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void startHevcPlayer() {
        Log.i("VtsPlayer", "startHevcPlayer");
        if (videoSocket != null) {
//            hevcSocket.setupSocket(urlSocket);
//            hevcSocket.startSocket();
            hevcSplitter.startSplitter();
            hevcDecoder.start();
        } else {
            sleep20milSec();
            startHevcPlayer();
        }
    }

    public void startAvcPlayer() {
        Log.i("VtsPlayer", "startAvcPlayer");
        if (videoSocket != null) {
//            avcSocket.setupSocket(urlSocket);
//            avcSocket.startSocket();
            avcSplitter.startSplitter();
            avcDecoder.start();
        } else {
            sleep20milSec();
            startAvcPlayer();
        }
    }

    // public void startAudioPlayer(){
    //     if (audioSocket != null) {
    //         audioSocket.setupSocket(urlSocketAudio);
    //         audioSocket.startSocket();
    //         audioSplitter.startSplitter();
    //         aacDecoder.start();
    //     }else {
    //         sleep20milSec();
    //         startAudioPlayer();
    //     }
    // }

    // public void stopAudioDecoder(){
    //     if(aacDecoder != null){
    //         aacDecoder.stop();
    //         Log.i("Stop","STOPPED >>>>>>>>>");
    //     }
    // }

//     public void startAudioDecoder(){
//         if (this.aacDecoder != null){
//             this.aacDecoder.start();
//         }else {
//             this.aacDecoder = new AacDecoder(this);
//             this.aacDecoder.start();
//         }
// //        Log.e("Stop","STARTED >>>>>>>>>"+this.aacDecoder);
//     }


    public void startPlayer(String token) {
        if(videoSocket != null){
            videoSocket.setupSocket(urlSocket,token);
            videoSocket.startSocket();
        }
        startHevcPlayer();
        startAvcPlayer();
        if (this.videoView != null) {
            this.videoView.setupVideoView();
        }
    }

    public void releaseHevcPlayer() {
        if (this.hevcDecoder != null) {
            this.hevcDecoder.stop();
            this.hevcDecoder = null;
        }
        if (this.hevcSplitter != null) {
            this.hevcSplitter.stopSplitter();
            this.hevcSplitter = null;
        }
//        if (this.hevcSocket != null) {
//            this.hevcSocket.stopSocket();
//            this.hevcSocket = null;
//        }
    }

    public void releaseAvcPlayer() {
        if (this.avcDecoder != null) {
            this.avcDecoder.stop();
            this.avcDecoder = null;
        }
        if (this.avcSplitter != null) {
            this.avcSplitter.stopSplitter();
            this.avcSplitter = null;
        }
//        if (this.avcSocket != null) {
//            this.avcSocket.stopSocket();
//            this.avcSocket = null;
//        }
    }

    // public void releaseAudioPlayer() {
    //     if (this.aacDecoder != null) {
    //         this.aacDecoder.stop();
    //         this.aacDecoder = null;
    //     }
    //     if (this.audioSplitter != null) {
    //         this.audioSplitter.stopSplitter();
    //         this.audioSplitter = null;
    //     }
    //     if (this.audioSocket != null) {
    //         this.audioSocket.stopSocket();
    //         this.audioSocket = null;
    //     }
    // }

    public void releasePlayer() {
        this.isStop = true;
        this.isPlaying = false;

        if (this.videoView != null) {
            this.videoView.release();
            this.videoView = null;
        }

        if (this.rtpQueue != null) {
            this.rtpQueue.empty();
            // this.rtpQueue.empty_();
            this.rtpQueue.emptyHevc();
            this.rtpQueue = null;
        }

        if (this.videoSocket != null) {
            this.videoSocket.stopSocket();
            this.videoSocket = null;
        }

        releaseAvcPlayer();

        releaseHevcPlayer();

        // releaseAudioPlayer();

        onPlayerStopped();
    }

    public interface IPlayerState {
        void errorPlayer();

        void loading();

        void playing();

        void stopVideo();
    }

    public void setPlayerState(IPlayerState iPlayerState) {
        this.iPlayerState = iPlayerState;
    }

    public void onPlayerPlaying() {
        if(!this.isPlaying){
            this.isPlaying = true;
            (new Handler(Looper.getMainLooper())).post((Runnable) (new Runnable() {
                public final void run() {
                    methodChannel.invokeMethod("sourceLoadDone", true);
                }
            }));
        }
        if (this.iPlayerState != null) {
            this.isPlaying = true;
            this.iPlayerState.playing();

        }
    }

    public void onPlayerError() {
        if (this.iPlayerState != null) {
            this.iPlayerState.errorPlayer();
            (new Handler(Looper.getMainLooper())).post((Runnable) (new Runnable() {
                public final void run() {
                    methodChannel.invokeMethod("sourceErrorClose", true);
                }
            }));
        }
    }

    public void onPlayerLoading() {
        if (this.iPlayerState != null) {
            this.isPlaying = false;
            this.iPlayerState.loading();
        }
    }

    private void onPlayerStopped() {
        if (this.iPlayerState != null) {
            this.isPlaying = false;
            this.iPlayerState.stopVideo();
        }
    }

//    public void enableZoom(boolean isZoom) {
//        if (this.videoView != null) {
//            this.videoView.enableZoom(isZoom);
//        }
//    }

    public boolean isPlaying() {
        return isPlaying;
    }
}
