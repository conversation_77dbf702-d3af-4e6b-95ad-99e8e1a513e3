package com.viettel.citizens.hanoi;

import io.flutter.plugin.common.PluginRegistry;

public class FirebaseCloudMessagingPlugin {
    public static void registerWith(PluginRegistry pluginRegistry){
        if(alreadyRegisterWith(pluginRegistry)) return;
        registerWith(pluginRegistry);
    }

    private static boolean alreadyRegisterWith(PluginRegistry pluginRegistry){
        String key = FirebaseCloudMessagingPlugin.class.getCanonicalName();
        if(pluginRegistry.hasPlugin(key)) return true;
        pluginRegistry.registrarFor(key);
        return false;
    }
}
