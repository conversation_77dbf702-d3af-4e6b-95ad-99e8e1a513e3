package com.viettel.citizens.hanoi.media.viewer

import android.Manifest
import android.annotation.SuppressLint
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import app.futured.hauler.DragDirection
import app.futured.hauler.OnDragActivityListener
import app.futured.hauler.OnDragDismissedListener
import com.viettel.citizens.hanoi.databinding.MediaViewerActivityBinding
import com.gg.gapo.core.download.download
import com.gg.gapo.core.navigation.AppDeepLink
import com.gg.gapo.core.navigation.deeplink.photo.viewer.MediaViewerDeepLink
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.GapoStrings
import com.gg.gapo.core.ui.snackbar.makeNormalSnackbar
import com.gg.gapo.core.utilities.bundle.getHeavyObjects
import com.gg.gapo.core.utilities.di.qualifier.GapoConstantQualifier
import com.gg.gapo.core.utilities.view.setDebouncedClickListener
import com.gg.gapo.core.utilities.view.viewpager.findFragmentAtPosition
import com.gg.gapo.core.workspace.domain.model.Feature
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.viettel.citizens.hanoi.media.viewer.adapter.ViewerMediaAdapter
import com.viettel.citizens.hanoi.media.viewer.model.MediaViewerMediaViewData
import com.viettel.citizens.hanoi.media.viewer.model.mapToViewData
import com.viettel.citizens.hanoi.media.viewer.transformer.MediaViewerDepthPageTransformer
import com.viettel.citizens.hanoi.media.viewer.video.MediaViewerVideoFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.core.qualifier.named
import org.koin.java.KoinJavaComponent

/**
 * <AUTHOR>
 * @since 23/12/2022
 * Move từ Common
 */
@AppDeepLink("media/viewer")
internal class MediaViewerActivity : AppCompatActivity() {

    private lateinit var binding: MediaViewerActivityBinding

    private val workspaceManager by inject<WorkspaceManager>()

    private var attachments = mutableListOf<MediaViewerMediaViewData>()
    private var initialPosition: Int = 0

    private val handler = Handler(Looper.getMainLooper())

    private val dragEnabledRunnable = Runnable {
        binding.haulerView.setDragEnabled(true)
    }

    private val isFeatureDownloadEnabled: Boolean
        get() = workspaceManager.currentWorkspace?.features?.isEnable(Feature.DOWNLOAD) ?: true

    private val downloadPermissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result ->
            if (result.isNotEmpty() && result.values.all { it }) {
                downloadMedia()
            } else {
                makeNormalSnackbar(GapoStrings.shared_permission_denied_msg)?.show()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val isFeatureCaptureScreenEnabled = try {
            KoinJavaComponent.getKoin().get<Boolean>(
                qualifier = named(GapoConstantQualifier.IS_ENABLE_CAPTURE_SCREEN)
            )
        } catch (e: Exception) {
            true
        }
        if (!isFeatureCaptureScreenEnabled) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
                window.setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE
                )
            }
        }
        binding = MediaViewerActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
        init()
    }

    @SuppressLint("MissingPermission")
    private fun init() {
        initialPosition =
            intent?.extras?.getInt(MediaViewerDeepLink.START_POSITION_EXTRA, 0) ?: 0
        val mediaList: List<MediaViewerMediaViewData> =
            intent?.extras?.getHeavyObjects<MediaViewerDeepLink.Media>(
                this,
                MediaViewerDeepLink.MEDIA_VIEWER_MEDIA_DATA_KEY
            ).orEmpty().mapToViewData()
        if (mediaList.isEmpty()) {
            finish()
            return
        }
        attachments.addAll(mediaList)
        binding.mediaSize.visibility = if (attachments.isNotEmpty()) {
            binding.mediaSize.text = "${initialPosition + 1}/${attachments.size}"
            View.VISIBLE
        } else {
            View.GONE
        }

        binding.pager.adapter = ViewerMediaAdapter(this, attachments)
        binding.pager.setPageTransformer(MediaViewerDepthPageTransformer())
        binding.pager.setCurrentItem(initialPosition, false)

        binding.pager.offscreenPageLimit = 1
        binding.pager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                if (attachments.isNotEmpty() && position >= 0 && position < attachments.size) {
                    binding.mediaSize.text = "${position + 1}/${attachments.size}"
                    setDragEnabled(true)
                    setPlayVideoAtIndex(position, 0)
                    val media = attachments[position]
                    binding.imageHd.isVisible = media.isHD()
                }
            }
        })
        setPlayVideoAtIndex(initialPosition, 2)
        binding.imageHd.isVisible = if (initialPosition < attachments.size) {
            attachments[initialPosition].isHD()
        } else attachments.first().isHD()

        binding.btnBack.setDebouncedClickListener {
            finish()
        }

        if (!isFeatureDownloadEnabled) {
            binding.actionDownload.visibility = View.GONE
        }
        binding.actionDownload.setDebouncedClickListener {
            downloadPermissionLauncher.launch(DOWNLOAD_IMAGE_PERMISSIONS)
        }

        binding.pager.setOnClickListener {
            binding.toolbar.visibility =
                if (binding.toolbar.visibility == View.VISIBLE) View.GONE else View.VISIBLE
        }

        binding.haulerView.setOnDragDismissedListener(object : OnDragDismissedListener {
            override fun onDismissed(dragDirection: DragDirection) {
                finish()
            }
        })

        binding.haulerView.setOnDragActivityListener(object : OnDragActivityListener {

            private var savedVisibleLayoutTop: Boolean? = null

            override fun onDrag(elasticOffsetPixels: Float, rawOffset: Float) {
                if (rawOffset != 0f) {
                    if (savedVisibleLayoutTop == null) {
                        savedVisibleLayoutTop = binding.toolbar.isVisible
                    }
                    binding.toolbar.isVisible = false
                    binding.haulerView.setBackgroundColor(
                        ContextCompat.getColor(
                            this@MediaViewerActivity,
                            GapoColors.transparent
                        )
                    )
                } else if (elasticOffsetPixels == 0f) {
                    binding.haulerView.setDragEnabled(false)
                    binding.toolbar.isVisible = savedVisibleLayoutTop ?: false
                    savedVisibleLayoutTop = null
                    binding.haulerView.setBackgroundColor(
                        ContextCompat.getColor(
                            this@MediaViewerActivity,
                            GapoColors.alwaysDarkPrimary
                        )
                    )
                    handler.postDelayed(dragEnabledRunnable, DRAG_ENABLED_DELAY)
                }
            }
        })
    }

    private fun setPlayVideoAtIndex(position: Int, second: Long = 1) {
        lifecycleScope.launch {
            delay(second)
            val currentFragment = binding.pager.findFragmentAtPosition(
                supportFragmentManager,
                binding.pager.currentItem
            )
            if (currentFragment is MediaViewerVideoFragment) {
                currentFragment.playVideo()
            }
            val previousIndex = position - 1
            val nextIndex = position + 1
            binding.pager.findFragmentAtPosition(supportFragmentManager, previousIndex)?.let {
                pauseVideo(it)
            }
            binding.pager.findFragmentAtPosition(supportFragmentManager, nextIndex)?.let {
                pauseVideo(it)
            }
        }
    }

    private fun pauseVideo(fragment: Fragment) {
        if (fragment is MediaViewerVideoFragment) {
            fragment.pauseVideo()
        }
    }

    fun setDragEnabled(enable: Boolean) {
        binding.haulerView.setDragEnabled(enable)
    }

    fun setToolbarVisible(visible: Int) {
        binding.toolbar.visibility = visible
    }

    fun isToolbarVisible() = binding.toolbar.isShown

    override fun onDestroy() {
        binding.pager.adapter = null
        handler.removeCallbacks(dragEnabledRunnable)
        super.onDestroy()
    }

    @SuppressLint("MissingPermission")
    private fun downloadMedia() {
        if (binding.pager.currentItem < attachments.size) {
            val mediaData = attachments[binding.pager.currentItem]
            download(mediaData.src)
        }
    }

    companion object {
        private const val DRAG_ENABLED_DELAY = 500L

        private val DOWNLOAD_IMAGE_PERMISSIONS =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) arrayOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            ) else arrayOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
    }
}
