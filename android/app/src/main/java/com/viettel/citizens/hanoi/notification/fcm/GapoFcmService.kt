package com.viettel.citizens.hanoi.notification.fcm

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.core.content.getSystemService
import androidx.core.graphics.drawable.toBitmap
import androidx.core.os.bundleOf
import com.viettel.citizens.hanoi.MainActivity
import com.viettel.citizens.hanoi.R
import com.bumptech.glide.Priority
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.gg.gapo.core.auth.manager.AuthManager
import com.gg.gapo.core.eventbus.fcm.FcmMessageReceivedBusEvent
import com.gg.gapo.core.eventbus.postEvent
import com.gg.gapo.core.fcm.manager.FcmManager
import com.gg.gapo.core.notification.GapoNotificationManager
import com.gg.gapo.core.ui.GapoAutoDimens
import com.gg.gapo.core.ui.GapoColors
import com.gg.gapo.core.ui.image.avatar.GapoAvatarPlaceholderGenerator
import com.gg.gapo.core.ui.notification.inapp.GapoInAppNotification
import com.gg.gapo.core.utilities.glide.GapoGlide
import com.gg.gapo.core.utilities.livedata.appForegroundLiveData
import com.gg.gapo.core.utilities.performace.bitmapConfig
import com.gg.gapo.core.workspace.manager.WorkspaceManager
import com.gg.gapo.livekit.call.center.CallCenterV2
import com.gg.gapo.messenger.presentation.events.ConversationDefaultUnRead
import com.viettel.citizens.hanoi.notification.fcm.cache.FcmInboxMessage
import com.viettel.citizens.hanoi.notification.fcm.cache.deleteFcmInboxMessage
import com.viettel.citizens.hanoi.notification.fcm.cache.getFcmInboxMessage
import com.viettel.citizens.hanoi.notification.fcm.cache.saveFcmInboxMessage
import com.viettel.citizens.hanoi.notification.fcm.cache.toInboxStyle
import com.viettel.citizens.hanoi.notification.fcm.event.OnClickNotificationInAppBusEvent
import com.viettel.citizens.hanoi.notification.fcm.model.mapToInAppNotification
import com.viettel.citizens.hanoi.notification.fcm.receiver.FcmDeleteIntentReceiver
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.viettel.citizens.hanoi.MainActivity.Companion.NOTIFICATION_PAYLOAD_EXTRA
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.android.ext.android.inject
import timber.log.Timber

class GapoFcmService : FirebaseMessagingService() {

    private val inAppNotificationCenter by inject<GapoInAppNotification>()

    private val callCenterV2 by inject<CallCenterV2>()

    private val fcmManager by inject<FcmManager>()

    private val authManager by inject<AuthManager>()

    private val workspaceManager by inject<WorkspaceManager>()

    private val notificationManager by lazy(LazyThreadSafetyMode.NONE) {
        getSystemService<NotificationManager>()
    }

    private val Map<String, String>.isMessenger: Boolean
        get() = !this[THREAD_ID_KEY].isNullOrEmpty()

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        if (!authManager.isLoggedIn || token.isEmpty()) return
        Timber.e("onNewToken = $token")
        fcmManager.registerToken(token)
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        val data = remoteMessage.data
        val source = data[SOURCE_KEY]
        if (source == "gapo") {
            if (!authManager.isLoggedIn || workspaceManager.currentWorkspace?.isEnabled != true || data.isEmpty()) return
            when (data[TYPE_KEY]) {
                VOIP_TYPE -> {
                    callCenterV2.handleFCMData(data)
                }
                HIDDEN_TYPE -> {
                    when (data[TARGET_ACTION_KEY]) {
                        HIDDEN_CLEAR_CHAT_TARGET -> {
                            // cancel tất cả thread chat đã đọc ở device khác dựa vào GROUP_KEY
                            // GROUP_KEY đảm nhận làm notification_id cho case noti gộp của chat
                            val groupKey = data[GROUP_KEY]
                            if (!groupKey.isNullOrEmpty()) {
                                deleteFcmInboxMessage(groupKey)
                                notificationManager?.cancel(groupKey.hashCode())
                            }
                        }
                        DELETE_MESSAGE_CHAT_TARGET -> {
                            val groupKey = data[GROUP_KEY]
                            val messageId = data[MESSAGE_ID_KEY]
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !groupKey.isNullOrEmpty() && !messageId.isNullOrEmpty()) {
                                val notificationId = groupKey.hashCode()
                                val notification =
                                    notificationManager?.activeNotifications?.firstOrNull { it.id == notificationId }?.notification
                                if (notification != null) {
                                    val messages = getFcmInboxMessage(groupKey).toMutableList()
                                    if (messages.removeAll { it.id == messageId }) {
                                        if (messages.isEmpty()) {
                                            deleteFcmInboxMessage(groupKey)
                                            notificationManager?.cancel(notificationId)
                                        } else {
                                            saveFcmInboxMessage(groupKey, messages)
                                            notificationManager?.notify(
                                                notificationId,
                                                NotificationCompat.Builder(this, notification)
                                                    .setContentText(messages.last().message)
                                                    .setStyle(messages.toInboxStyle())
                                                    .build()
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else -> notify(remoteMessage)
            }
        } else {
            //TODO:iHanoi notification flow
        }
    }

    fun registerToken(token: String) {
        if (!authManager.isLoggedIn || token.isEmpty()) return
        fcmManager.registerToken(token)
    }

    fun consumeMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        val data = remoteMessage.data
        if (!authManager.isLoggedIn || workspaceManager.currentWorkspace?.isEnabled != true || data.isEmpty()) return
        when (data[TYPE_KEY]) {
            VOIP_TYPE -> {
                callCenterV2.handleFCMData(data)
            }
            HIDDEN_TYPE -> {
                when (data[TARGET_ACTION_KEY]) {
                    HIDDEN_CLEAR_CHAT_TARGET -> {
                        // cancel tất cả thread chat đã đọc ở device khác dựa vào GROUP_KEY
                        // GROUP_KEY đảm nhận làm notification_id cho case noti gộp của chat
                        val groupKey = data[GROUP_KEY]
                        if (!groupKey.isNullOrEmpty()) {
                            deleteFcmInboxMessage(groupKey)
                            notificationManager?.cancel(groupKey.hashCode())
                        }
                    }
                    DELETE_MESSAGE_CHAT_TARGET -> {
                        val groupKey = data[GROUP_KEY]
                        val messageId = data[MESSAGE_ID_KEY]
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !groupKey.isNullOrEmpty() && !messageId.isNullOrEmpty()) {
                            val notificationId = groupKey.hashCode()
                            val notification =
                                notificationManager?.activeNotifications?.firstOrNull { it.id == notificationId }?.notification
                            if (notification != null) {
                                val messages = getFcmInboxMessage(groupKey).toMutableList()
                                if (messages.removeAll { it.id == messageId }) {
                                    if (messages.isEmpty()) {
                                        deleteFcmInboxMessage(groupKey)
                                        notificationManager?.cancel(notificationId)
                                    } else {
                                        saveFcmInboxMessage(groupKey, messages)
                                        notificationManager?.notify(
                                            notificationId,
                                            NotificationCompat.Builder(this, notification)
                                                .setContentText(messages.last().message)
                                                .setStyle(messages.toInboxStyle())
                                                .build()
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else -> notify(remoteMessage)
        }
    }

    private fun notify(remoteMessage: RemoteMessage) {
        val data = remoteMessage.data
        if (data.isNotEmpty()) {
            Timber.e("onMessageReceived = $data")
            if (appForegroundLiveData.value == true) {
                if (!data.isMessenger) {
                    FcmMessageReceivedBusEvent(data).postEvent()
                    notifyInAppNotification(data)
                }
            } else {
                FcmMessageReceivedBusEvent(data).postEvent()
                notifySystemNotification(remoteMessage)
            }
        }
    }

    private fun notifyInAppNotification(data: Map<String, String>) {
        try {
            val flashMessage = data.mapToInAppNotification() ?: return
            inAppNotificationCenter.notify(
                GapoInAppNotification.Payload(
                    title = flashMessage.title.orEmpty(),
                    message = flashMessage.bodySpanned,
                    icon = flashMessage.icon.orEmpty(),
                    image = flashMessage.image.orEmpty(),
                    imagePlaceholder = GapoAvatarPlaceholderGenerator.generateCircle(
                        this,
                        flashMessage.subjectName.orEmpty(),
                        GapoAutoDimens._32dp
                    )
                ),
                onClick = {
                    OnClickNotificationInAppBusEvent(
                        GapoNotificationManager.Payload(
                            workspaceId = flashMessage.workspaceId,
                            url = flashMessage.url,
                            type = flashMessage.type,
                            notifyIds = flashMessage.notifyIds,
                            tracking = flashMessage.tracking
                        )
                    ).postEvent()
                },
                onShow = {
                    if (flashMessage.shouldPlaySound) {
                        playSound(R.raw.sound_flash_message)
                    }
                }
            )
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    private fun notifySystemNotification(remoteMessage: RemoteMessage) {
        val data = remoteMessage.data
        val title = if (data.containsKey(TITLE_KEY)) {
            data[TITLE_KEY].orEmpty()
        } else {
            remoteMessage.notification?.title.orEmpty()
        }
        val body = if (data.containsKey(BODY_KEY)) {
            data[BODY_KEY].orEmpty()
        } else {
            remoteMessage.notification?.body.orEmpty()
        }

        val channelId = "work.vn.gapo.app"

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "iHanoi",
                NotificationManager.IMPORTANCE_HIGH
            )
            channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
            notificationManager?.createNotificationChannel(channel)
        }

        val builder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setColor(ContextCompat.getColor(this, GapoColors.accentWorkPrimary))
            .setContentTitle(title).setContentText(body).setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH).setDefaults(Notification.DEFAULT_ALL)

        try {
            val bitmap = extractBitmap(data)
            if (bitmap != null) {
                builder.setLargeIcon(bitmap)
            }
        } catch (e: Exception) {
            Timber.e(e)
        }

        if (data.containsKey(SOUND_KEY)) {
            try {
                builder.setSound(Uri.parse("android.resource://com.gg.gapo.messenger/raw/${data[SOUND_KEY]}"))
            } catch (e: Exception) {
                Timber.e(e)
            }
        }

        val groupKey = data[GROUP_KEY]

        if (groupKey.isNullOrEmpty()) {
            val notificationId = System.currentTimeMillis().toInt()
            builder.setContentIntent(getPendingIntent(notificationId, data))
            notificationManager?.notify(notificationId, builder.build())
        } else {
            val messageId = data[MESSAGE_ID_KEY]
            if (messageId.isNullOrEmpty()) return
            val messages = getFcmInboxMessage(groupKey).takeLast(5).toMutableList()
            if (messages.add(FcmInboxMessage(messageId, body))) {
                saveFcmInboxMessage(groupKey, messages)
                builder.setStyle(messages.toInboxStyle())
                val notificationId = groupKey.hashCode()
                builder.setContentIntent(
                    getPendingIntent(
                        notificationId,
                        data
                    )
                )
                builder.setDeleteIntent(
                    getDeletePendingIntent(
                        notificationId,
                        groupKey
                    )
                )
                notificationManager?.notify(notificationId, builder.build())
            }
        }
    }

    private fun Context.playSound(idRes: Int) {
        try {
            val uri = Uri.parse("android.resource://$packageName/$idRes")
            val ringTone = RingtoneManager.getRingtone(this, uri)
            ringTone.play()
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    private fun extractBitmap(data: Map<String, String>): Bitmap? {
        val url = data[THREAD_AVATAR_KEY] ?: data[IMAGE_THUMB_KEY] ?: data[IMAGE_KEY]
        val name = data[THREAD_NAME_KEY] ?: data[SUBJECT_NAME_KEY] ?: "iHanoi"
        val drawable =
            GapoAvatarPlaceholderGenerator.generateCircle(this, name, GapoAutoDimens._32dp)
        val size = resources.getDimensionPixelSize(GapoAutoDimens._32dp)
        return if (url.isNullOrEmpty()) {
            drawable.toBitmap(size, size, bitmapConfig)
        } else {
            GapoGlide.with(this).asBitmap().load(url).circleCrop()
                .apply(RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL))
                .priority(Priority.IMMEDIATE).timeout(1000).submit(size, size).get()
        }
    }

    private fun getPendingIntent(
        notificationId: Int,
        map: Map<String, String>
    ): PendingIntent {
        val payload = GapoNotificationManager.Payload(
            workspaceId = map[WS_ID_KEY],
            url = map[URL_KEY],
            type = map[TYPE_KEY],
            threadId = map[THREAD_ID_KEY],
            threadType = map[THREAD_TYPE_KEY],
            notifyIds = map[NOTIFY_IDS_KEY],
            tracking = map[TRACKING_KEY]
        )

        val intent = Intent(this, MainActivity::class.java).apply {
            putExtras(bundleOf(NOTIFICATION_PAYLOAD_EXTRA to payload))
        }

        return PendingIntent.getActivity(
            this,
            notificationId,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
    }

    private fun getDeletePendingIntent(
        notificationId: Int,
        groupKey: String
    ): PendingIntent {
        return PendingIntent.getBroadcast(
            this,
            notificationId,
            FcmDeleteIntentReceiver.newIntent(this, groupKey),
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    internal fun onUnreadConversationCountEvent(event: ConversationDefaultUnRead) {
        Timber.e("onUnreadConversationCountEvent = ${event.count}")
    }

    companion object {
        internal const val FCM_BUNDLE_DATA_EXTRA = "fcm_bundle_data_extra"

        internal const val TYPE_KEY = "type"
        internal const val SOURCE_KEY = "service"
        private const val TITLE_KEY = "title"
        internal const val BODY_KEY = "body"
        internal const val URL_KEY = "url"
        internal const val GROUP_KEY = "group_key"
        internal const val THREAD_ID_KEY = "thread_id"
        internal const val THREAD_TYPE_KEY = "thread_type"
        private const val MESSAGE_ID_KEY = "message_id"
        private const val SOUND_KEY = "sound"
        internal const val TRACKING_KEY = "tracking"
        internal const val WS_ID_KEY = "ws_id"
        internal const val NOTIFY_IDS_KEY = "notify_ids"
        internal const val CALENDAR_SYNC_SUCCEEDED_KEY = "sync_successful" // 1 or null
        internal const val CALENDAR_SYNC_SUCCEEDED_VALUE = "1"
        private const val THREAD_AVATAR_KEY = "thread_avatar"
        private const val THREAD_NAME_KEY = "thread_name"
        internal const val SUBJECT_NAME_KEY = "subjectName"
        internal const val IMAGE_KEY = "image"
        private const val IMAGE_THUMB_KEY = "imageThumb"
        private const val TARGET_ACTION_KEY = "target_action"
        private const val VERSION_KEY = "version"

        internal const val MINI_TASK_TYPE = "mini-task"
        internal const val CALENDAR_TYPE = "calendar"
        internal const val GROUP_POST_SINGLE_TYPE = "gp_single"
        internal const val CHAT_TYPE = "chat"
        private const val VOIP_TYPE = "voip"
        private const val VIDEO_PROCESSING_COMPLETE_TYPE = "video_processing_complete"
        internal const val INVITED_TO_WORKSPACE_TYPE = "wp_user_invite_group"
        internal const val REJECT_WORKSPACE_TYPE = "wp_admin_reject_join_request"
        internal const val HIDDEN_TYPE = "hidden"

        internal const val HIDDEN_CLEAR_CHAT_TARGET = "clear_chat"
        internal const val DELETE_MESSAGE_CHAT_TARGET = "delete_message"
    }
}
