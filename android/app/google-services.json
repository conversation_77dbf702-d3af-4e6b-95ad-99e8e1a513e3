{"project_info": {"project_number": "105682256683", "project_id": "ha-noi-b98b8", "storage_bucket": "ha-noi-b98b8.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:105682256683:android:afb87b6d538ab4740c7b6e", "android_client_info": {"package_name": "com.viettel.citizens.cds"}}, "oauth_client": [{"client_id": "105682256683-4ihenk5utl8s17ir21ia8en7cc9d9crq.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD6b6vgtzEcLEPhuXd7QMY4NunhAr00ZaM"}, {"current_key": "AIzaSyBRBUd6cS5ntRf6_WACFpWo4kQNokmT1nQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "105682256683-4ihenk5utl8s17ir21ia8en7cc9d9crq.apps.googleusercontent.com", "client_type": 3}, {"client_id": "105682256683-2oaf2ucsc7hjdbuqh0h6vjno4pl94irr.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.viettel.government.hanoi"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:105682256683:android:f406a707e7386afe0c7b6e", "android_client_info": {"package_name": "com.viettel.citizens.hanoi"}}, "oauth_client": [{"client_id": "105682256683-4ihenk5utl8s17ir21ia8en7cc9d9crq.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD6b6vgtzEcLEPhuXd7QMY4NunhAr00ZaM"}, {"current_key": "AIzaSyBRBUd6cS5ntRf6_WACFpWo4kQNokmT1nQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "105682256683-4ihenk5utl8s17ir21ia8en7cc9d9crq.apps.googleusercontent.com", "client_type": 3}, {"client_id": "105682256683-2oaf2ucsc7hjdbuqh0h6vjno4pl94irr.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.viettel.government.hanoi"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:105682256683:android:5506e78119a82e5c0c7b6e", "android_client_info": {"package_name": "com.viettel.government.hanoi"}}, "oauth_client": [{"client_id": "105682256683-4ihenk5utl8s17ir21ia8en7cc9d9crq.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD6b6vgtzEcLEPhuXd7QMY4NunhAr00ZaM"}, {"current_key": "AIzaSyBRBUd6cS5ntRf6_WACFpWo4kQNokmT1nQ"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "105682256683-4ihenk5utl8s17ir21ia8en7cc9d9crq.apps.googleusercontent.com", "client_type": 3}, {"client_id": "105682256683-2oaf2ucsc7hjdbuqh0h6vjno4pl94irr.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.viettel.government.hanoi"}}]}}}], "configuration_version": "1"}