[{"directory": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android/.cxx/Debug/5l5u4b3b/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dbbhelper_EXPORTS -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DBUILD_FOR_ANDROID -o CMakeFiles/bbhelper.dir/c++/directBufferAndroid.cpp.o -c /Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android/c++/directBufferAndroid.cpp", "file": "/Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android/c++/directBufferAndroid.cpp"}]