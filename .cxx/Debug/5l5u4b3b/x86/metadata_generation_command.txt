                    -H/Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Development/Viettel/citizen-mobile-app/build/pdf_render/intermediates/cxx/Debug/5l5u4b3b/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Development/Viettel/citizen-mobile-app/build/pdf_render/intermediates/cxx/Debug/5l5u4b3b/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android/.cxx/Debug/5l5u4b3b/x86
-GNinja
                    Build command args: []
                    Version: 2