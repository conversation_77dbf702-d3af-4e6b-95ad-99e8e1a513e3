{"artifacts": [{"path": "/Users/<USER>/Development/Viettel/citizen-mobile-app/build/pdf_render/intermediates/cxx/RelWithDebInfo/52282z1m/obj/x86_64/libbbhelper.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"command": 1, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}, {"backtrace": 2, "fragment": "-DBUILD_FOR_ANDROID"}], "defines": [{"define": "bbhelper_EXPORTS"}], "language": "CXX", "sourceIndexes": [0], "sysroot": {"path": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot"}}], "id": "bbhelper::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/sysroot"}}, "name": "bbhelper", "nameOnDisk": "libbbhelper.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "c++/directBufferAndroid.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}