{"buildFiles": ["/Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android/.cxx/RelWithDebInfo/52282z1m/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/.pub-cache/hosted/pub.dev/pdf_render-1.4.0/android/.cxx/RelWithDebInfo/52282z1m/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"bbhelper::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "bbhelper", "output": "/Users/<USER>/Development/Viettel/citizen-mobile-app/build/pdf_render/intermediates/cxx/RelWithDebInfo/52282z1m/obj/arm64-v8a/libbbhelper.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}